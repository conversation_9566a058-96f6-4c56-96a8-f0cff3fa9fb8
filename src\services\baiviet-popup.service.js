import { http } from "./Base/base.service";
export const tintucService = {
    async getList(query) {
        return await http.get('/api/v1/admins/posts', {
            params: query
        })
    },
    async create(payload) {
        return await http.post('/api/v1/admins/posts', payload)
    },
    async update(id, payload) {
        return await http.put('/api/v1/admins/posts/' + id, payload)

    },
    async getDetail(id) {
        return await http.get(`/api/v1/admins/posts/${id}`)
    },
    async delete(id) {
        return await http.delete(`/api/v1/admins/posts/${id}`)
    },

    async loginStore(id) {
        return await http.get(`/store-login/${id}`)
    },

    async upload(payload) {
        return await http.post('/api/v1/admins/upload', payload, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
    },
}
