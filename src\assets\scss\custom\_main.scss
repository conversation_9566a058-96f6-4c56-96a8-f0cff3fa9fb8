//
// Custom Main
//
// Add your own styles or override existing ones
//
// This file is included last, so you have access
// to all Bootstrap and OneUI functions/mixins/styles etc
// --------------------------------------------------

// If you have many custom styles, you can create and import additional files
// For example you can create _component1.scss and include it as follows:
// @import 'component1';
:root {
  --el-color-primary: #a48ad4 !important;
}

.el-date-editor {
  width: 100% !important;
}
.el-dialog__title {
  color: #212529 !important;
  font-weight: 500 !important;
  font-size: 16px !important;
}
.el-dialog {
  padding: 0 !important;
}
.dialog-footer {
  background-color: #ebeef2 !important;
  padding: 20px !important;
  border-radius: 0 0 0.375rem 0.375rem !important;
}
.el-dialog__header {
  padding: 10px 20px !important;
  background-color: #ebeef2 !important;
  border-radius: 0.375rem 0.375rem 0 0 !important;
}
.el-form-item__label {
  color: #212529 !important;
  font-weight: 500 !important;
  font-size: 15px !important;
}
