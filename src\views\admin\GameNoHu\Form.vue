<script setup>
import { gameService } from "@/services/game.service";
import { useTemplateStore } from "@/stores/template";
import { onMounted, ref, computed } from "vue";
import VueSelect from "vue-select";
import useNotify from "@/composables/useNotify";

const store = useTemplateStore();
const { setNotify } = useNotify();

const optionsStore = ref([]);
const onFetchListSanhGame = async () => {
  try {
    const response = await gameService.getList({
      limit: -1,
    });
    if (!response?.error) {
      const newArray = response?.data?.map((item) => ({
        value: item.id,
        name: item.name,
      }));
      optionsStore.value = [...newArray];
    }
  } catch (e) {
    console.log(e);
  }
};

const optionRole = ref([
  {
    value: 1,
    label: "Admin",
  },
  {
    value: 3,
    label: "User",
  },
]);

const props = defineProps(["v$", "state", "idModal"]);

const { v$, state } = props;

const apiUrl = import.meta.env.VITE_API_URL.replace(/\/$/, "");

const fullAvatarUrl = computed(() => {
  if (!state.avatar) return "";
  // console.log("Full avatar URL:", `${apiUrl}${state.avatar}`);
  return `${apiUrl}${state.avatar}`;
});

const fileInputRef = ref(null);

const triggerFileSelect1 = () => {
  fileInputRef.value?.click();
};

const handleFileChange = async (event) => {
  const file = event.target.files[0];
  if (!file) return;
  const formData = new FormData();
  formData.append("file", file);
  formData.append("type", "avatar");
  let payload;
  payload = formData;
  try {
    store.pageLoader({ mode: "on" });
    const response = await gameService.upload(payload);
    state.avatar = response?.path;
    state.avatar = `${apiUrl}${state.avatar}`;
    store.pageLoader({ mode: "off" });
  } catch (e) {
    console.log(e);
    store.pageLoader({ mode: "off" });
    setNotify({
      title: "Error",
      message: "Không đúng định dạng ảnh",
    });
  }
};

const changeActive = () => {
  state.isActive = !state.isActive;
  state.is_active = state.isActive ? 1 : 0;
};

onMounted(async () => {
  try {
    store.pageLoader({ mode: "on" });
    await onFetchListSanhGame();
    store.pageLoader({ mode: "off" });
    state.email = "";
    state.password = "";
  } catch (error) {
    store.pageLoader({ mode: "off" });
  }
});
</script>

<template>
  <div class="content">
    <div class="row justify-content-start">
      <div class="col-sm-12 col-md-12">
        <div class="row justify-content-center">
          <div class="col-sm-10 col-md-12">
            <div class="mb-4">
              <label class="form-label" for="block-form-name-id"
                >Tên game <span class="text-danger">*</span></label
              >
              <input
                type="text"
                class="form-control"
                id="block-form-name-id"
                name="block-form-name-id"
                placeholder="Enter name.."
                :class="{
                  'is-invalid': v$.name.$errors.length,
                }"
                v-model="state.name"
              />
              <div
                v-if="v$.name.$errors.length"
                class="invalid-feedback animated fadeIn"
              >
                Please fill name
              </div>
            </div>
            <div class="mb-4">
              <label class="form-label" for="block-form-email-id"
                >Sảnh <span class="text-danger">*</span></label
              >

              <select
                id="val-store-id"
                class="form-select"
                :class="{
                  'is-invalid': v$.game_id.$errors.length,
                }"
                v-model="state.game_id"
                placeholder="Select store"
              >
                <option
                  v-for="(store, index) in optionsStore"
                  :value="store.value"
                  :key="`store-${index}`"
                >
                  {{ store.name }}
                </option>
              </select>
              <div
                v-if="v$.game_id.$errors.length"
                class="invalid-feedback animated fadeIn"
              >
                Please fill game_id
              </div>
            </div>
            <div class="mb-4">
              <label class="form-label" for="block-form-email-id"
                >Avatar <span class="text-danger">*</span></label
              >
              <input
                ref="fileInputRef"
                type="file"
                class="form-control"
                id="block-form-email-id"
                name="block-form-email-id"
                placeholder="Enter avatar.."
                :class="{
                  'is-invalid': v$.avatar.$errors.length,
                }"
                accept="image/*"
                @change="handleFileChange($event)"
                style="display: none"
              />
              <button
                style="margin-left: 10px"
                @click.prevent="triggerFileSelect1"
                class="btn btn-primary"
              >
                Chọn ảnh đại diện
              </button>
              <div style="margin-top: 10px" v-if="state.avatar">
                <img :src="`${state.avatar}`" alt="Avatar" width="100" />
              </div>
              <div
                v-if="v$.avatar.$errors.length"
                class="invalid-feedback animated fadeIn"
              >
                Please upload avatar
              </div>
            </div>
            <div class="mb-4">
              <label class="form-label" for="val-type">Active Game</label>
              <div class="form-check form-switch">
                <input
                  class="form-check-input"
                  type="checkbox"
                  @change="changeActive"
                  :checked="state.is_active === 1 ? true : false"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
@import "vue-select/dist/vue-select.css";
@import "@/assets/scss/vendor/vue-select";
.icon_arrow_left {
  background-color: rgb(243, 235, 235);
  border-radius: 100%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.error {
  width: 100%;
  margin-top: 0.375rem;
  font-size: 0.875rem;
  color: var(--bs-form-invalid-color);
}
</style>
