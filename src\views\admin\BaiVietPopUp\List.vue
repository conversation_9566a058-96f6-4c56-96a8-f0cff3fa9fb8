<script setup>
import EButton from "@/components/Elements/EButton.vue";
import EListEmpty from "@/components/Elements/EListEmpty.vue";
import EModal from "@/components/Elements/EModal.vue";
import useNotify from "@/composables/useNotify";
import { tintucService } from "@/services/tintuc.service";
import { useTemplateStore } from "@/stores/template";
import ModalFormCode from "@/views/admin/BaiVietPopUp/Form.vue";
import useVuelidate from "@vuelidate/core";
import { required, sameAs } from "@vuelidate/validators";
import { userService } from "@/services/user.service";
import Swal from "sweetalert2";
import { computed, onMounted, reactive, ref, watch } from "vue";
import ChangePassword from "@/views/admin/MultiTenant/ChangePassword.vue";
import { useForm } from "vee-validate";
import * as yup from "yup";

import {
  Dataset,
  DatasetInfo,
  DatasetItem,
  DatasetPager,
  DatasetSearch,
  DatasetShow,
} from "vue-dataset";
import { useRoute } from "vue-router";

let toast = Swal.mixin({
  buttonsStyling: false,
  target: "#page-container",
  customClass: {
    confirmButton: "btn btn-success m-1",
    cancelButton: "btn btn-danger m-1",
    input: "form-control",
  },
});
const store = useTemplateStore();
const { setNotify } = useNotify();
const route = useRoute();
const id = route.params?.id;
let state = reactive([
  {
    title: "",
    avatar: null,
  },
]);

const refBtn = ref(null);
const refBtnPassword = ref(null);
const domModal = ref("changePassword");
onMounted(() => {
  refBtn.value = document.getElementById("closeModal");
  refBtnPassword.value = document.getElementById("closeModal" + domModal.value); // Gán refBtn cho một phần tử DOM
});

const rules = computed(() => {
  return {
    title: {
      required,
    },
    avatar: {
      required,
    },
  };
});

let vformMultiTenant$ = useVuelidate(rules, state);
const validationSchema = yup.object({
  content: yup.string().trim(),
});

const { values, handleSubmit, setFieldValue, resetForm, setFieldError } =
  useForm({
    validationSchema,
    initialValues: {
      content: null,
    },
  });

// Helper variables
const cols = reactive([
  {
    name: "Tên tin tức",
    field: "name",
    sort: "",
  },
  {
    name: "Nội dung",
    field: "status",
    sort: "",
  },
]);

const limit = ref(10);
const visible = ref(true);
const currentPage = ref(1);
const totalPage = ref(1);
const total = ref();
const listTinTuc = ref([]);
const idModal = ref(null);
const searchNameTinTuc = ref("");
const searchTinTuc = async () => {
  currentPage.value = 1;
  await onFetchList();
};
watch([limit], async () => {
  currentPage.value = 1;
  await onFetchList();
});
const searchGame = ref(0);
const listGame = ref();
watch([searchGame], async () => {
  currentPage.value = 1;
  await onFetchList();
});
const onFetchList = async () => {
  try {
    let payload;
    if (searchNameTinTuc.value) {
      payload = {
        page: currentPage.value,
        limit: limit.value,
        search: searchNameTinTuc.value,
      };
    } else {
      payload = {
        page: currentPage.value,
        limit: limit.value,
      };
    }
    store.pageLoader({ mode: "on" });
    const response = await tintucService.getList(payload);
    if (!response?.error) {
      listTinTuc.value = response?.data || [];
      totalPage.value = response?.total_page;
      total.value = response?.total;
    }
    store.pageLoader({ mode: "off" });
  } catch (error) {
    console.log(error);
    store.pageLoader({ mode: "off" });
  }
};

const handleModalForm = async () => {
  idModal.value = undefined;
  vformMultiTenant$.value.$reset();
  store.pageLoader({ mode: "on" });
  store.pageLoader({ mode: "off" });
};
const handleModalFormUpdate = async (id) => {
  idModal.value = id;
  store.pageLoader({ mode: "on" });
  await apiGetPrinter(id);
  store.pageLoader({ mode: "off" });
};

const apiGetPrinter = async (id) => {
  const response = await tintucService.getDetail(id);
  vformMultiTenant$.value.$reset();
};

let stateChangePassword = reactive({
  newPassword: null,
  confirmPassword: null,
});

const idMultiTenant = ref();

onMounted(async () => {
  await onFetchList();

  // Remove labels from
  document.querySelectorAll("#datasetLength label").forEach((el) => {
    el.remove();
  });

  // Replace select classes
  let selectLength = document.querySelector("#datasetLength select");

  if (selectLength) {
    selectLength.classList = "";
    selectLength.classList.add("form-select");
    selectLength.style.width = "80px";
  }
});

async function onSubmitMultiTenant() {
  if (idModal.value) {
    await onSubmitUpdateMultiTenant();
  } else {
    await onSubmitCreateMultiTenant();
  }
}
async function confirmNew(val) {
  await onSubmitCreateMultiTenant(val);
}

async function onSubmitCreateMultiTenant(val) {
  try {
    const result = await vformMultiTenant$.value.$validate();
    if (!result) return;
    // if (!values.content) return;
    let payload = {};
    payload = {
      title: state.title,
      content: state.content,
      short_description: state.short_description,
      avatar_url: state.avatar,
      is_active: state.is_active ? 1 : 0,
    };
    const response = await tintucService.create(payload);
    if (!response?.error) {
      onFetchList();
      if (!val) {
        refBtn.value.click();
      }
      (state.name = ""),
        (state.content = ""),
        setFieldValue("content", ""),
        (state.short_description = ""),
        (state.is_active = 1),
        (state.avatar = null),
        vformMultiTenant$.value.$reset();
      return setNotify({
        title: "Success",
        message: "Create success",
        type: "success",
      });
    }
  } catch (e) {
    return setNotify({
      title: "Error",
      message: e?.message,
    });
  }
}

function stripHtml(html) {
  const tmp = document.createElement("DIV");
  tmp.innerHTML = html;
  return (
    tmp.textContent.replace(/\n/g, " ") ||
    tmp.innerText.replace(/\n/g, " ") ||
    ""
  );
}

async function onSubmitUpdateMultiTenant() {
  try {
    const result = await vformMultiTenant$.value.$validate();
    if (!result) return;
    // if (!values.content) return;
    let payload = {
      title: state.title,
      content: state.content,
      short_description: state.short_description,
      avatar_url: state.avatar,
      is_active: state.is_active ? 1 : 0,
    };
    const response = await tintucService.update(idModal.value, payload);
    if (!response?.error) {
      onFetchList();
      refBtn.value.click();
      return setNotify({
        title: "Success",
        message: "Update success",
        type: "success",
      });
    }
  } catch (e) {
    return setNotify({
      title: "Error",
      message: e?.message,
    });
  }
}

const onOpenDeleteConfirm = (id) => {
  toast
    .fire({
      title: "Bạn có chắc chắn muốn xóa?",
      text: "Bạn chắc chắn muốn xóa bài viết này!",
      icon: "warning",
      showCancelButton: true,
      customClass: {
        confirmButton: "btn btn-danger m-1",
        cancelButton: "btn btn-info m-1",
      },
      confirmButtonText: "Yes, delete!",
      html: false,
      preConfirm: () => {
        return tintucService.delete(id);
      },
    })
    .then((result) => {
      if (result.dismiss === "cancel") {
        return toast.fire("Cancelled", "Hủy xóa bài viết thành công.", "error");
      }
      toast.fire("Deleted!", "Xóa bài viết thành công.", "success");
      onFetchList();
    });
};

async function submitIndex(row) {
  try {
    let payload = {
      index: row.index,
    };
    const response = await tintucService.update(row.id, payload);
    if (!response?.error) {
      onFetchList();
      refBtn.value.click();
      return setNotify({
        title: "Success",
        message: "Update success",
        type: "success",
      });
    }
  } catch (e) {
    return setNotify({
      title: "Error",
      message: e?.message,
    });
  }
}

const changeHot = async (id) => {
  const url = `https://68gaming.pro/post?id=${id}`;
  window.open(url, "_blank");
};

const onCloseMultiTenant = () => {
  vformMultiTenant$.value.$reset();
};
</script>

<template>
  <BasePageHeading
    title="Danh sách bài viết pop up"
    subtitle=""
    :go-back="true"
  >
    <template #extra>
      <div class="flex flex-col">
        <div class="d-flex justify-content-end mb-2">
          <EButton
            style="margin-right: 10px"
            type="info"
            size="sm"
            data-bs-toggle="modal"
            data-bs-target="#modal-multi-tenant"
            data-target=".bd-example-modal-lg"
            @click="() => handleModalForm()"
            ><i class="fa fa-plus opacity-50 me-1"></i> Thêm bài viết pop
            up</EButton
          >
        </div>
      </div>
    </template>
  </BasePageHeading>

  <div class="content">
    <BaseBlock title="Danh sách bài viết pop up">
      <Dataset
        v-slot="{ ds }"
        :ds-data="listTinTuc"
        :ds-search-in="['id', 'name']"
      >
        <div>
          <div class="row" :data-page-count="ds.dsPagecount">
            <div id="datasetLength" class="col-md-4 py-2">
              <DatasetShow v-show="false" :dsShowEntries="100" />
              <div class="form-inline">
                <select class="form-select" style="width: 80px" v-model="limit">
                  <option :value="5">5</option>
                  <option :value="10">10</option>
                  <option :value="25">25</option>
                  <option :value="50">50</option>
                  <option :value="100">100</option>
                </select>
              </div>
            </div>
            <!-- <div class="col-md-4 py-2">
              <select
                id="val-manager-id"
                class="form-select"
                v-model="searchGame"
                placeholder="Select "
              >
                <option
                  v-for="(role, index) in listGame"
                  :value="role.value"
                  :key="`role-${index}`"
                >
                  {{ role.name }}
                </option>
              </select>
            </div> -->

            <div class="col-md-3 py-2">
              <input
                type="text"
                class="form-control"
                v-model="searchNameTinTuc"
                placeholder="Search..."
                @keyup.enter="searchTinTuc"
              />
            </div>
            <div class="col-md-1 py-2">
              <button
                type="button"
                class="btn btn-primary"
                @click="searchTinTuc"
              >
                Search
              </button>
            </div>
          </div>
          <hr />
          <div class="row" v-if="listTinTuc?.length">
            <div class="col-md-12">
              <div class="table-responsive">
                <table class="table mb-0">
                  <thead>
                    <tr>
                      <th v-for="th in cols" :key="th.field">
                        {{ th.name }}
                      </th>
                      <th class="text-end" scope="col">Action</th>
                    </tr>
                  </thead>
                  <DatasetItem tag="tbody" class="fs-sm">
                    <template #default="{ row }">
                      <tr>
                        <td style="min-width: 100px">{{ row?.title }}</td>
                        <td style="min-width: 150px">
                          {{ stripHtml(row?.content.slice(0, 100)) }}
                          <span
                            v-if="row?.content.length > 100"
                            class="text-primary"
                            >...</span
                          >
                        </td>

                        <td class="text-end">
                          <div class="btn-group">
                            <button
                              type="button"
                              data-bs-toggle="modal"
                              data-bs-target="#modal-multi-tenant"
                              data-target=".bd-example-modal-lg"
                              class="btn btn-sm btn-alt-secondary"
                              @click="() => handleModalFormUpdate(row?.id)"
                            >
                              <i class="fa fa-fw fa-pencil-alt"></i>
                            </button>
                            <button
                              type="button"
                              class="btn btn-sm btn-alt-secondary"
                              @click="onOpenDeleteConfirm(row?.id)"
                            >
                              <i class="fa fa-fw fa-times"></i>
                            </button>
                            <button
                              v-if="row?.is_active"
                              type="button"
                              class="btn btn-sm btn-alt-secondary"
                              @click="changeHot(row?.id)"
                            >
                              <i class="fa fa-fw fa-eye"></i>
                            </button>
                          </div>
                        </td>
                      </tr>
                    </template>
                  </DatasetItem>
                </table>
              </div>
            </div>
          </div>
          <div
            class="d-flex flex-md-row flex-column justify-content-between align-items-center"
          >
            <DatasetInfo class="py-3 fs-sm" />
            <el-pagination
              v-if="visible"
              v-model:current-page="currentPage"
              @current-change="onFetchList"
              background
              v-model:page-size="limit"
              layout="prev, pager, next"
              prev-text="Prev"
              next-text="Next"
              :total="total"
            />
          </div>
        </div>
        <EListEmpty v-if="!listTinTuc?.length" />
      </Dataset>
    </BaseBlock>
  </div>
  <form @submit.prevent="onSubmitMultiTenant">
    <EModal
      id="modal-multi-tenant"
      :title="idModal ? 'Cập nhật tin tức' : 'Thêm tin tức'"
      :idModal="idModal"
      size="modal-xl"
      ok-text="Confirm"
      ok-text-add="Confirm & New"
      ok-type="submit"
      :close-on-submit="false"
      @confirm="() => onSubmitMultiTenant()"
      @confirmNew="(val) => confirmNew(val)"
      @closeModal="() => onCloseMultiTenant()"
    >
      <template v-slot:childrenComponent>
        <ModalFormCode
          :idModal="idModal"
          :v$="vformMultiTenant$"
          :state="state"
        />
      </template>
    </EModal>
  </form>
  <form @submit.prevent="onSubmitPassword">
    <EModal
      id="modal-change-password-multi-tenant"
      title="Change Password"
      ok-text="Change Password"
      ok-type="submit"
      :close-on-submit="false"
      :is-show-text-add="false"
      :dom-modal="domModal"
      @confirm="() => onSubmitPassword()"
      @closeModal="() => onCloseChangePassword()"
    >
      <template v-slot:childrenComponent>
        <ChangePassword :v$="vChangePassword$" :state="stateChangePassword" />
      </template>
    </EModal>
  </form>
</template>

<style lang="scss" scoped>
.gg-select {
  box-sizing: border-box;
  position: relative;
  display: block;
  transform: scale(1);
  width: 22px;
  height: 22px;
}
.gg-select::after,
.gg-select::before {
  content: "";
  display: block;
  box-sizing: border-box;
  position: absolute;
  width: 8px;
  height: 8px;
  left: 7px;
  transform: rotate(-45deg);
}
.gg-select::before {
  border-left: 2px solid;
  border-bottom: 2px solid;
  bottom: 4px;
  opacity: 0.3;
}
.gg-select::after {
  border-right: 2px solid;
  border-top: 2px solid;
  top: 4px;
  opacity: 0.3;
}
th.sort {
  cursor: pointer;
  user-select: none;
  &.asc {
    .gg-select::after {
      opacity: 1;
    }
  }
  &.desc {
    .gg-select::before {
      opacity: 1;
    }
  }
}
.ql-container {
  height: 150px !important;
}
</style>
