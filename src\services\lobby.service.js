import { http } from "./Base/base.service";
export const lobbyService = {
    async getList(query) {
        return await http.get('/api/v1/admins/lobbies', {
            params: query
        })
    },
    async create(payload) {
        return await http.post('/api/v1/admins/lobbies', payload)
    },
    async update(id, payload) {
        return await http.put('/api/v1/admins/lobbies/' + id, payload)
    },
    async getDetail(id) {
        return await http.get(`/api/v1/admins/lobbies/${id}`)
    },
    async delete(id) {
        return await http.delete(`/top-managers/${id}`)
    },

    async loginStore(id) {
        return await http.get(`/store-login/${id}`)
    },

    async upload(payload) {
        return await http.post('/api/v1/admins/upload', payload, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
    },
}