import { http } from "./Base/base.service";
export const gameService = {
    async getList(query) {
        return await http.get('/api/v1/admins/games', {
            params: query
        })
    },
    async create(payload) {
        return await http.post('/api/v1/admins/games', payload)
    },
    async update(id, payload) {
        return await http.put('/api/v1/admins/games/' + id, payload)
    },
    async getDetail(id) {
        return await http.get(`/api/v1/admins/games/${id}`)
    },
    async delete(id) {
        return await http.delete(`/top-managers/${id}`)
    },

    async loginStore(id) {
        return await http.get(`/store-login/${id}`)
    },

    async upload(payload) {
        return await http.post('/api/v1/admins/upload', payload, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
    },
}